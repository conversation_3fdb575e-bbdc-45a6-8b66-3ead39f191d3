import os
os.environ["TOKENIZERS_PARALLELISM"] = "true"
import argparse
import random
import json

from transformers import AutoTokenizer
import torch

from gliner import GLiNERConfig, GLiNER
from gliner.training import Trainer, TrainingArguments
from gliner.data_processing.collator import DataCollatorWithPadding, DataCollator
from gliner.utils import load_config_as_namespace
from gliner.data_processing import Words<PERSON><PERSON>litter, GLiNERDataset
from gliner.addons.confidence_scorer import ConfidenceScorer
from gliner.evaluation import Evaluator


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', type=str, default= "configs/config.yaml")
    parser.add_argument('--log_dir', type=str, default = 'models_wnut16/')
    parser.add_argument('--compile_model', type=bool, default = False)
    parser.add_argument('--freeze_language_model', type=bool, default = False)
    parser.add_argument('--new_data_schema', type=bool, default = False)
    parser.add_argument('--train_confidence_scorer', type=bool, default = True)
    args = parser.parse_args()
    
    device = torch.device('cuda:0') if torch.cuda.is_available() else torch.device('cpu')

    config = load_config_as_namespace(args.config)
    config.log_dir = args.log_dir

    with open(config.train_data, 'r') as f:
        train_data = json.load(f)

    # Load dev data if provided
    dev_data = None
    if config.dev_data:
        with open(config.dev_data, 'r') as f:
            dev_data = json.load(f)

    # Load test data if provided
    test_data = None
    if hasattr(config, 'test_data') and config.test_data:
        with open(config.test_data, 'r') as f:
            test_data = json.load(f)

    print('Train dataset size:', len(train_data))
    if dev_data:
        print('Dev dataset size:', len(dev_data))
    if test_data:
        print('Test dataset size:', len(test_data))
        
    #shuffle
    random.shuffle(train_data)
    print('Train dataset is shuffled...')


    if config.prev_path is not None:
        tokenizer = AutoTokenizer.from_pretrained(config.prev_path)
        model = GLiNER.from_pretrained(config.prev_path)
        model_config = model.config
    else:
        model_config = GLiNERConfig(**vars(config))
        tokenizer = AutoTokenizer.from_pretrained(model_config.model_name)
    
        words_splitter = WordsSplitter(model_config.words_splitter_type)

        model = GLiNER(model_config, tokenizer=tokenizer, words_splitter=words_splitter)

        if not config.labels_encoder:
            model_config.class_token_index=len(tokenizer)
            tokenizer.add_tokens([model_config.ent_token, model_config.sep_token], special_tokens=True)
            model_config.vocab_size = len(tokenizer)
            model.resize_token_embeddings([model_config.ent_token, model_config.sep_token], 
                                        set_class_token_index = False,
                                        add_tokens_to_tokenizer=False)

    if args.compile_model:
        torch.set_float32_matmul_precision('high')
        model.to(device)
        model.compile_for_training()
        
    if args.freeze_language_model:
        model.model.token_rep_layer.bert_layer.model.requires_grad_(False)
    else:
        model.model.token_rep_layer.bert_layer.model.requires_grad_(True)

    if args.new_data_schema:
        train_dataset = GLiNERDataset(train_data, model_config, tokenizer, words_splitter)
        dev_dataset = GLiNERDataset(dev_data, model_config, tokenizer, words_splitter) if dev_data else None
        test_dataset = GLiNERDataset(test_data, model_config, tokenizer, words_splitter) if test_data else None
        data_collator = DataCollatorWithPadding(model_config)
    else:
        train_dataset = train_data
        dev_dataset = dev_data
        test_dataset = test_data
        data_collator = DataCollator(model.config, data_processor=model.data_processor, prepare_labels=True)

    training_args = TrainingArguments(
        output_dir=config.log_dir,
        learning_rate=float(config.lr_encoder),
        weight_decay=float(config.weight_decay_encoder),
        others_lr=float(config.lr_others),
        others_weight_decay=float(config.weight_decay_other),
        focal_loss_gamma=config.loss_gamma,
        focal_loss_alpha=config.loss_alpha,
        lr_scheduler_type=config.scheduler_type,
        warmup_ratio=config.warmup_ratio,
        per_device_train_batch_size=config.train_batch_size,
        per_device_eval_batch_size=config.train_batch_size,
        max_grad_norm=config.max_grad_norm,
        max_steps=config.num_steps,
        evaluation_strategy="epoch",
        save_steps = config.eval_every,
        save_total_limit=config.save_total_limit,
        dataloader_num_workers = 8,
        use_cpu = False,
        report_to="none",
        bf16=True,
        )

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=dev_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator,
    )
    trainer.train()

    # Final evaluation on the test set
    if test_dataset:
        print("\nEvaluating on the test set...")

        # a. 收集标签
        all_labels = list({ner[2] for item in test_data for ner in item.get("ner", [])})

        # b. 将 tokenized_text 列表拼回句子字符串
        test_texts = [" ".join(item["tokenized_text"]) for item in test_data]

        # c. 预测
        test_preds = model.run(
            texts=test_texts,
            labels=all_labels,
            batch_size=config.train_batch_size,
            return_span_info=False          # 评估阶段只要实体
        )

        # d. 计算指标
        gold_entities = [item["ner"] for item in test_data]
        
        # Convert character indices to token indices for evaluation
        formatted_predictions = []
        for i, (predictions, item) in enumerate(zip(test_preds, test_data)):
            # Create character-to-token mapping for this sample
            char_to_token = {}
            char_pos = 0
            for token_idx, token in enumerate(item['tokenized_text']):
                # Map each character position to its token index
                for _ in range(len(token)):
                    char_to_token[char_pos] = token_idx
                    char_pos += 1
                # Skip the space
                char_pos += 1
            
            # Map character positions to token positions
            sample_preds = []
            for entity in predictions:
                try:
                    # Find closest token indices
                    char_start = entity['start']
                    char_end = entity['end']
                    
                    # Find token that contains the start character
                    token_start = None
                    for pos in range(char_start, -1, -1):
                        if pos in char_to_token:
                            token_start = char_to_token[pos]
                            break
                    
                    # Find token that contains the end character
                    token_end = None
                    max_pos = len(" ".join(item['tokenized_text']))
                    for pos in range(min(char_end, max_pos-1), -1, -1):
                        if pos in char_to_token:
                            token_end = char_to_token[pos]
                            break
                    
                    if token_start is not None and token_end is not None:
                        sample_preds.append((token_start, token_end, entity['label']))
                except Exception as e:
                    print(f"Error mapping entity {entity} to tokens: {e}")
                    continue
                    
            formatted_predictions.append(sample_preds)
        
        evaluator = Evaluator(gold_entities, formatted_predictions)
        detailed_metrics, f1 = evaluator.evaluate()

        print(f"\nTest-set F1  : {f1*100:.2f}%")
        print("Detailed metrics:\n", detailed_metrics)


    # ---------- 2) 训练置信度分类器 ----------
    if args.train_confidence_scorer:
        if dev_data is None:
            raise ValueError("请在 config.yaml 中提供 dev_data，才能训练置信度分类器。")

        print("\nTraining confidence scorer ...")
        confidence_scorer = ConfidenceScorer()

        # a. 收集标签
        all_labels = list({ner[2] for item in dev_data for ner in item.get("ner", [])})

        # b. 拼接为字符串
        dev_texts = [" ".join(item["tokenized_text"]) for item in dev_data]

        # c. 预测并取 span_info
        dev_preds, dev_span_infos = model.run(
            texts=dev_texts,
            labels=all_labels,
            batch_size=config.train_batch_size,
            return_span_info=True
        )

        # d. 逐条匹配真值并收集训练样本
        for i, (pred_ents, gold_ents) in enumerate(zip(dev_preds, dev_data)):
            gold = gold_ents["ner"]
            
            # 构建字符位置到token位置的映射（用于gold实体，它们是token位置）
            char_to_token = {}
            token_text = gold_ents["tokenized_text"]
            char_pos = 0
            for token_idx, token in enumerate(token_text):
                # 将每个字符位置映射到其token索引
                for _ in range(len(token)):
                    char_to_token[char_pos] = token_idx
                    char_pos += 1
                # 跳过空格
                char_pos += 1
            
            # 逐一检查预测实体
            for j, pred_ent in enumerate(pred_ents):
                if j < len(dev_span_infos[i]):
                    span_info = dev_span_infos[i][j]
                    
                    # 找到对应的token位置
                    try:
                        # 获取预测实体的字符位置
                        char_start = pred_ent["start"]
                        char_end = pred_ent["end"]
                        
                        # 转换为token位置
                        token_start = None
                        token_end = None
                        
                        # 寻找包含起始字符的token
                        for pos in range(char_start, -1, -1):
                            if pos in char_to_token:
                                token_start = char_to_token[pos]
                                break
                        
                        # 寻找包含结束字符的token
                        max_pos = len(" ".join(token_text))
                        for pos in range(min(char_end, max_pos-1), -1, -1):
                            if pos in char_to_token:
                                token_end = char_to_token[pos]
                                break
                        
                        if token_start is not None and token_end is not None:
                            # 现在与gold实体进行比较（使用token位置）
                            is_correct = any(
                                pred_ent["label"].lower() == g[2].lower() and
                                token_start == g[0] and
                                token_end == g[1] for g in gold
                            )
                            
                            # 收集训练样本
                            confidence_scorer.collect_training_data(span_info, is_correct)
                            
                    except Exception as e:
                        print(f"处理实体时出错: {e}")

        # e. 训练与交叉验证
        try:
            if len(confidence_scorer.features_list) > 0:
                print(f"收集了 {len(confidence_scorer.features_list)} 个训练样本...")
                train_accuracy = confidence_scorer.train()
                if train_accuracy > 0:
                    confidence_scorer.cross_validate(cv=5)
                    # f. 保存
                    os.makedirs(config.log_dir, exist_ok=True)
                    scorer_path = os.path.join(config.log_dir, "confidence_scorer.joblib")
                    confidence_scorer.save(scorer_path)
                    print(f"Confidence scorer saved to {scorer_path}")
                else:
                    print("置信度分类器训练失败，无法保存模型")
            else:
                print("没有收集到足够的训练样本，无法训练置信度分类器")
        except Exception as e:
            print(f"训练置信度分类器时出错: {e}")
            import traceback
            traceback.print_exc()
